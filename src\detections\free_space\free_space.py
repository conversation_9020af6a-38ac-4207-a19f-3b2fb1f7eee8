from typing import NamedTuple

import cv2 as cv
import joblib
import matplotlib.pyplot as plt
import numpy as np
from skimage import future

from src.detections.free_space.lir_basis import all_interior_rectangles
from src.detections.free_space.model.free_space_segment_trainer import features_func


class EmptyRectArea(NamedTuple):
    u: int
    v: int
    w: int
    h: int

    r: int = 0
    theta: int = 0
    z: int = 0


def sort_to_preference(candidates: list[EmptyRectArea], max_candidates: int = 3) -> list:
    """
    Sorts the candidates to prefer the lowest possible position, next prefer larger sizes.
    """
    candidates = sorted(
        candidates, key=lambda candidate: candidate.v + candidate.h, reverse=True
    )  # Prefer lowest possible position
    candidates = candidates[:max_candidates]
    candidates = sorted(candidates, key=lambda candidate: candidate.w * candidate.h, reverse=True)
    return candidates


def generate_candidates(image: np.ndarray, max_candidates: int = 3) -> list[EmptyRectArea]:
    image = cv.threshold(image, 254, 255, cv.THRESH_BINARY_INV)[1]
    image = cv.dilate(image, cv.getStructuringElement(cv.MORPH_RECT, (5, 5)), iterations=2)
    image = cv.erode(image, cv.getStructuringElement(cv.MORPH_RECT, (5, 5)), iterations=200)
    image = cv.dilate(image, cv.getStructuringElement(cv.MORPH_RECT, (5, 5)), iterations=125)
    contours, _ = cv.findContours(image, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)
    candidates = []
    for contour in contours:
        x, y, w, h = cv.boundingRect(contour)
        x = max(0, x)
        y = max(0, y)
        w = min(image.shape[1], w)
        h = min(image.shape[0], h)
        candidates.append(EmptyRectArea(x, y, w, h))
    candidates = sort_to_preference(candidates, max_candidates=max_candidates)
    return candidates


def preprocess_profile_image(profile: np.ndarray) -> np.ndarray:
    """
    Code currently from Nippon - Label check
    """
    profile = np.array(profile, dtype=np.float64)
    profile = cv.rotate(profile, cv.ROTATE_90_COUNTERCLOCKWISE)
    profile = cv.flip(profile, 1)

    threshval = profile.min() / 1e2
    profile[profile <= threshval] = 0

    profile = -profile / 1e6

    blurred = cv.blur(profile, (34, 92))
    retval = profile - blurred

    retval[retval < 1e-4] = 1e-3
    retval[retval > 5e-3] = 1e-2

    retval = cv.normalize(retval, retval, 0, 255, cv.NORM_MINMAX, cv.CV_8UC1)
    retval = cv.morphologyEx(
        retval, cv.MORPH_CLOSE, cv.getStructuringElement(cv.MORPH_RECT, (5, 5)), iterations=3
    )
    return retval


def main():
    file = 'data/OCR/20250415/pointclouds/20241203_132659942244.npy'
    data = np.load(file)
    image = preprocess_profile_image(data)
    # image = cv.imread(
    #     'data/OCR/20250415/images/images_processed/20250415_102820052292_prep.png',
    #     cv.IMREAD_GRAYSCALE,
    # )
    image = image[242:2242, :]
    image = cv.resize(image, (0, 0), fx=0.25, fy=0.25, interpolation=cv.INTER_NEAREST)
    segmenter = joblib.load('src/detections/free_space/model/free_space_segmenter.joblib')
    segmentation = future.predict_segmenter(features_func(image), segmenter)
    print(segmentation)
    # Convert to boolean: True for clean areas (1), False for text areas (2)
    binary_mask = segmentation == 1
    # rect = find_largest_rectangle(binary_mask)
    rects = all_interior_rectangles(binary_mask, min_width=100, min_height=100, min_area=10000)
    print(rects)

    show = cv.cvtColor(image, cv.COLOR_GRAY2RGB)
    # plt.imshow(image, cmap='gray')
    # plt.show()
    for rect in rects:
        cv.rectangle(
            show,
            (rect[0], rect[1]),
            (rect[0] + rect[2] - 1, rect[1] + rect[3] - 1),
            (0, 255, 0),
            10,
        )
    plt.imshow(show)
    plt.show()


if __name__ == '__main__':
    main()
